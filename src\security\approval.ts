import { EventEmitter } from 'events';
import inquirer from 'inquirer';
import chalk from 'chalk';
import { logger } from '@/utils/logger';
import type { 
  ApprovalMode, 
  ToolCall, 
  RiskAssessment, 
  RiskLevel 
} from '@/types';

export interface ApprovalRequest {
  toolCall: ToolCall;
  riskAssessment: RiskAssessment;
  timestamp: Date;
}

export class ApprovalSystem extends EventEmitter {
  private mode: ApprovalMode;
  private autoApprovedCommands: Set<string> = new Set();
  private blockedCommands: Set<string> = new Set();

  constructor(mode: ApprovalMode = 'suggest') {
    super();
    this.mode = mode;
    this.initializeDefaultRules();
  }

  private initializeDefaultRules(): void {
    // Commands that are generally safe to auto-approve
    const safeCommands = [
      'ls', 'dir', 'pwd', 'whoami', 'date', 'echo',
      'cat', 'head', 'tail', 'grep', 'find', 'locate',
      'ps', 'top', 'df', 'du', 'free', 'uptime',
      'git status', 'git log', 'git diff', 'git show',
      'npm list', 'npm outdated', 'npm audit',
      'node --version', 'npm --version', 'git --version',
    ];

    safeCommands.forEach(cmd => this.autoApprovedCommands.add(cmd));

    // Commands that should always be blocked or require explicit approval
    const dangerousCommands = [
      'rm -rf /', 'rm -rf *', 'format', 'fdisk',
      'dd if=', 'mkfs', 'shutdown', 'reboot', 'halt',
      'del /s', 'rmdir /s', 'format c:',
    ];

    dangerousCommands.forEach(cmd => this.blockedCommands.add(cmd));
  }

  setMode(mode: ApprovalMode): void {
    this.mode = mode;
    logger.info('Approval mode changed', { mode });
  }

  getMode(): ApprovalMode {
    return this.mode;
  }

  async requestApproval(
    toolCall: ToolCall, 
    riskAssessment: RiskAssessment
  ): Promise<boolean> {
    const request: ApprovalRequest = {
      toolCall,
      riskAssessment,
      timestamp: new Date(),
    };

    this.emit('approval-required', request);

    // Check if command is blocked
    if (this.isCommandBlocked(toolCall)) {
      logger.warn('Command blocked by security policy', { 
        tool: toolCall.name,
        command: this.getCommandString(toolCall) 
      });
      return false;
    }

    // Auto-approve based on mode and risk level
    if (this.shouldAutoApprove(toolCall, riskAssessment)) {
      logger.debug('Command auto-approved', { 
        tool: toolCall.name,
        riskLevel: riskAssessment.level 
      });
      return true;
    }

    // Request user approval
    return await this.promptUserApproval(request);
  }

  private shouldAutoApprove(
    toolCall: ToolCall, 
    riskAssessment: RiskAssessment
  ): boolean {
    switch (this.mode) {
      case 'full-auto':
        return riskAssessment.level !== 'critical';
      
      case 'auto-edit':
        // Auto-approve file operations and safe commands
        if (toolCall.name.startsWith('file_') && riskAssessment.level === 'safe') {
          return true;
        }
        return this.isCommandAutoApproved(toolCall) && riskAssessment.level === 'safe';
      
      case 'suggest':
      default:
        return false; // Always ask for approval
    }
  }

  private async promptUserApproval(request: ApprovalRequest): Promise<boolean> {
    const { toolCall, riskAssessment } = request;
    
    console.log('\n' + chalk.yellow('🔒 Approval Required'));
    console.log(chalk.cyan('Tool:'), toolCall.name);
    console.log(chalk.cyan('Risk Level:'), this.formatRiskLevel(riskAssessment.level));
    
    if (toolCall.name === 'shell') {
      console.log(chalk.cyan('Command:'), toolCall.arguments.command);
      if (toolCall.arguments.args && Array.isArray(toolCall.arguments.args)) {
        console.log(chalk.cyan('Arguments:'), toolCall.arguments.args.join(' '));
      }
    } else {
      console.log(chalk.cyan('Arguments:'), JSON.stringify(toolCall.arguments, null, 2));
    }

    if (riskAssessment.reasons.length > 0) {
      console.log(chalk.yellow('Risk Factors:'));
      riskAssessment.reasons.forEach(reason => {
        console.log(chalk.yellow('  • '), reason);
      });
    }

    if (riskAssessment.suggestions.length > 0) {
      console.log(chalk.blue('Suggestions:'));
      riskAssessment.suggestions.forEach(suggestion => {
        console.log(chalk.blue('  • '), suggestion);
      });
    }

    const choices = [
      { name: 'Approve', value: 'approve' },
      { name: 'Deny', value: 'deny' },
      { name: 'Approve and remember for this session', value: 'approve-remember' },
    ];

    if (riskAssessment.level === 'critical') {
      choices.unshift({ name: 'Cancel (recommended for critical risk)', value: 'deny' });
    }

    const answer = await inquirer.prompt([
      {
        type: 'list',
        name: 'decision',
        message: 'Do you want to proceed?',
        choices,
        default: riskAssessment.level === 'critical' ? 'deny' : 'approve',
      },
    ]);

    const approved = answer.decision === 'approve' || answer.decision === 'approve-remember';

    if (answer.decision === 'approve-remember') {
      this.rememberApproval(toolCall);
    }

    logger.info('User approval decision', { 
      tool: toolCall.name,
      approved,
      riskLevel: riskAssessment.level 
    });

    return approved;
  }

  private rememberApproval(toolCall: ToolCall): void {
    const commandKey = this.getCommandKey(toolCall);
    this.autoApprovedCommands.add(commandKey);
    logger.debug('Command remembered for auto-approval', { command: commandKey });
  }

  private isCommandBlocked(toolCall: ToolCall): boolean {
    const commandString = this.getCommandString(toolCall);
    
    for (const blocked of this.blockedCommands) {
      if (commandString.toLowerCase().includes(blocked.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  }

  private isCommandAutoApproved(toolCall: ToolCall): boolean {
    const commandKey = this.getCommandKey(toolCall);
    const commandString = this.getCommandString(toolCall);
    
    // Check exact match
    if (this.autoApprovedCommands.has(commandKey)) {
      return true;
    }
    
    // Check partial matches for known safe commands
    for (const approved of this.autoApprovedCommands) {
      if (commandString.toLowerCase().startsWith(approved.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  }

  private getCommandKey(toolCall: ToolCall): string {
    if (toolCall.name === 'shell') {
      const command = toolCall.arguments.command as string;
      const args = (toolCall.arguments.args as string[]) || [];
      return `${command} ${args.join(' ')}`.trim();
    }
    
    return `${toolCall.name}:${JSON.stringify(toolCall.arguments)}`;
  }

  private getCommandString(toolCall: ToolCall): string {
    if (toolCall.name === 'shell') {
      const command = toolCall.arguments.command as string;
      const args = (toolCall.arguments.args as string[]) || [];
      return `${command} ${args.join(' ')}`.trim();
    }
    
    return toolCall.name;
  }

  private formatRiskLevel(level: RiskLevel): string {
    const colors = {
      safe: chalk.green,
      low: chalk.blue,
      medium: chalk.yellow,
      high: chalk.red,
      critical: chalk.bgRed.white,
    };
    
    return colors[level](level.toUpperCase());
  }

  // Configuration methods
  addAutoApprovedCommand(command: string): void {
    this.autoApprovedCommands.add(command);
    logger.debug('Command added to auto-approved list', { command });
  }

  removeAutoApprovedCommand(command: string): void {
    this.autoApprovedCommands.delete(command);
    logger.debug('Command removed from auto-approved list', { command });
  }

  addBlockedCommand(command: string): void {
    this.blockedCommands.add(command);
    logger.debug('Command added to blocked list', { command });
  }

  removeBlockedCommand(command: string): void {
    this.blockedCommands.delete(command);
    logger.debug('Command removed from blocked list', { command });
  }

  getAutoApprovedCommands(): string[] {
    return Array.from(this.autoApprovedCommands);
  }

  getBlockedCommands(): string[] {
    return Array.from(this.blockedCommands);
  }

  clearSessionApprovals(): void {
    // Remove session-specific approvals (keep default ones)
    const defaultCommands = new Set([
      'ls', 'dir', 'pwd', 'whoami', 'date', 'echo',
      'cat', 'head', 'tail', 'grep', 'find', 'locate',
      'ps', 'top', 'df', 'du', 'free', 'uptime',
      'git status', 'git log', 'git diff', 'git show',
      'npm list', 'npm outdated', 'npm audit',
      'node --version', 'npm --version', 'git --version',
    ]);
    
    this.autoApprovedCommands = defaultCommands;
    logger.info('Session approvals cleared');
  }

  // Statistics and monitoring
  getApprovalStats(): {
    mode: ApprovalMode;
    autoApprovedCount: number;
    blockedCount: number;
  } {
    return {
      mode: this.mode,
      autoApprovedCount: this.autoApprovedCommands.size,
      blockedCount: this.blockedCommands.size,
    };
  }
}
