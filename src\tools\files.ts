import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import type { ToolResult, FileOperation } from '@/types';

export class FileTool {
  async read(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const encoding = (args.encoding as BufferEncoding) || 'utf-8';

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);
      
      // Check if file exists
      await fs.access(resolvedPath);
      
      // Get file stats
      const stats = await fs.stat(resolvedPath);
      
      if (stats.isDirectory()) {
        throw new Error('Path is a directory, not a file');
      }

      // Check file size (limit to 10MB for safety)
      if (stats.size > 10 * 1024 * 1024) {
        throw new Error('File too large (>10MB). Use file streaming for large files.');
      }

      const content = await fs.readFile(resolvedPath, encoding);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: content,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async write(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const content = args.content as string;
      const encoding = (args.encoding as BufferEncoding) || 'utf-8';
      const backup = args.backup as boolean || false;

      if (!filePath) {
        throw new Error('File path is required');
      }

      if (content === undefined) {
        throw new Error('Content is required');
      }

      const resolvedPath = path.resolve(filePath);
      
      // Create backup if requested and file exists
      if (backup) {
        try {
          await fs.access(resolvedPath);
          const backupPath = `${resolvedPath}.backup.${Date.now()}`;
          await fs.copyFile(resolvedPath, backupPath);
          logger.debug('Backup created', { original: resolvedPath, backup: backupPath });
        } catch (error) {
          // File doesn't exist, no backup needed
        }
      }

      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });

      // Write file
      await fs.writeFile(resolvedPath, content, encoding);

      const stats = await fs.stat(resolvedPath);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: `File written successfully: ${resolvedPath} (${stats.size} bytes)`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
          backup,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async create(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const content = (args.content as string) || '';
      const encoding = (args.encoding as BufferEncoding) || 'utf-8';

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);
      
      // Check if file already exists
      try {
        await fs.access(resolvedPath);
        throw new Error('File already exists');
      } catch (error) {
        // File doesn't exist, which is what we want
        if ((error as any).code !== 'ENOENT') {
          throw error;
        }
      }

      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });

      // Create file
      await fs.writeFile(resolvedPath, content, encoding);

      const stats = await fs.stat(resolvedPath);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: `File created successfully: ${resolvedPath} (${stats.size} bytes)`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async delete(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const recursive = args.recursive as boolean || false;
      const force = args.force as boolean || false;

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);
      
      // Check if path exists
      await fs.access(resolvedPath);
      
      const stats = await fs.stat(resolvedPath);
      
      if (stats.isDirectory()) {
        if (!recursive) {
          throw new Error('Path is a directory. Use recursive=true to delete directories.');
        }
        await fs.rm(resolvedPath, { recursive: true, force });
      } else {
        await fs.unlink(resolvedPath);
      }

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: `${stats.isDirectory() ? 'Directory' : 'File'} deleted successfully: ${resolvedPath}`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          type: stats.isDirectory() ? 'directory' : 'file',
          recursive,
          force,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async move(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const source = args.source as string;
      const destination = args.destination as string;

      if (!source || !destination) {
        throw new Error('Source and destination paths are required');
      }

      const resolvedSource = path.resolve(source);
      const resolvedDestination = path.resolve(destination);
      
      // Check if source exists
      await fs.access(resolvedSource);
      
      // Ensure destination directory exists
      const destDir = path.dirname(resolvedDestination);
      await fs.mkdir(destDir, { recursive: true });

      // Move file/directory
      await fs.rename(resolvedSource, resolvedDestination);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: `Moved successfully: ${resolvedSource} → ${resolvedDestination}`,
        executionTime: Date.now() - startTime,
        metadata: {
          source: resolvedSource,
          destination: resolvedDestination,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async copy(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const source = args.source as string;
      const destination = args.destination as string;
      const recursive = args.recursive as boolean || false;

      if (!source || !destination) {
        throw new Error('Source and destination paths are required');
      }

      const resolvedSource = path.resolve(source);
      const resolvedDestination = path.resolve(destination);
      
      // Check if source exists
      await fs.access(resolvedSource);
      
      const stats = await fs.stat(resolvedSource);
      
      // Ensure destination directory exists
      const destDir = path.dirname(resolvedDestination);
      await fs.mkdir(destDir, { recursive: true });

      if (stats.isDirectory()) {
        if (!recursive) {
          throw new Error('Source is a directory. Use recursive=true to copy directories.');
        }
        await this.copyDirectory(resolvedSource, resolvedDestination);
      } else {
        await fs.copyFile(resolvedSource, resolvedDestination);
      }

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: `Copied successfully: ${resolvedSource} → ${resolvedDestination}`,
        executionTime: Date.now() - startTime,
        metadata: {
          source: resolvedSource,
          destination: resolvedDestination,
          type: stats.isDirectory() ? 'directory' : 'file',
          recursive,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async search(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const pattern = args.pattern as string;
      const cwd = (args.cwd as string) || process.cwd();
      const maxDepth = (args.maxDepth as number) || 10;

      if (!pattern) {
        throw new Error('Search pattern is required');
      }

      const files = await glob(pattern, {
        cwd: path.resolve(cwd),
        maxDepth,
        dot: false,
      });

      const results = files.slice(0, 100); // Limit results for performance

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: results.length > 0 
          ? `Found ${results.length} files:\n${results.join('\n')}`
          : 'No files found matching the pattern',
        executionTime: Date.now() - startTime,
        metadata: {
          pattern,
          cwd,
          maxDepth,
          resultCount: results.length,
          results,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async grep(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const pattern = args.pattern as string;
      const files = args.files as string[];
      const recursive = args.recursive as boolean || false;
      const ignoreCase = args.ignoreCase as boolean || false;
      const lineNumbers = args.lineNumbers as boolean || true;

      if (!pattern) {
        throw new Error('Search pattern is required');
      }

      if (!files || files.length === 0) {
        throw new Error('Files to search are required');
      }

      const regex = new RegExp(pattern, ignoreCase ? 'gi' : 'g');
      const results: string[] = [];

      for (const file of files) {
        try {
          const resolvedPath = path.resolve(file);
          const stats = await fs.stat(resolvedPath);

          if (stats.isDirectory() && recursive) {
            // Search in directory recursively
            const dirFiles = await glob('**/*', { 
              cwd: resolvedPath,
              nodir: true,
              maxDepth: 5,
            });

            for (const dirFile of dirFiles) {
              const fullPath = path.join(resolvedPath, dirFile);
              const matches = await this.searchInFile(fullPath, regex, lineNumbers);
              if (matches.length > 0) {
                results.push(`${fullPath}:\n${matches.join('\n')}`);
              }
            }
          } else if (stats.isFile()) {
            const matches = await this.searchInFile(resolvedPath, regex, lineNumbers);
            if (matches.length > 0) {
              results.push(`${resolvedPath}:\n${matches.join('\n')}`);
            }
          }
        } catch (error) {
          // Skip files that can't be read
          logger.debug('Skipping file in grep', { file, error });
        }
      }

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output: results.length > 0 
          ? results.join('\n\n')
          : 'No matches found',
        executionTime: Date.now() - startTime,
        metadata: {
          pattern,
          files,
          recursive,
          ignoreCase,
          lineNumbers,
          matchCount: results.length,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async copyDirectory(source: string, destination: string): Promise<void> {
    await fs.mkdir(destination, { recursive: true });
    
    const entries = await fs.readdir(source, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(source, entry.name);
      const destPath = path.join(destination, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  private async searchInFile(filePath: string, regex: RegExp, lineNumbers: boolean): Promise<string[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      const matches: string[] = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (regex.test(line)) {
          const match = lineNumbers 
            ? `${i + 1}: ${line}`
            : line;
          matches.push(match);
        }
      }

      return matches;
    } catch (error) {
      return [];
    }
  }
}
