#!/usr/bin/env node

const path = require('path');
const fs = require('fs');

// Check if we're running from source or built
const srcPath = path.join(__dirname, '..', 'src', 'index.ts');
const distPath = path.join(__dirname, '..', 'dist', 'index.js');

if (fs.existsSync(distPath)) {
  // Running from built version
  require(distPath);
} else if (fs.existsSync(srcPath)) {
  // Running from source, use tsx for TypeScript execution
  try {
    require('tsx/cli').main([srcPath, ...process.argv.slice(2)]);
  } catch (error) {
    console.error('Error: tsx not found. Please run "npm run build" first or install tsx globally.');
    console.error('npm install -g tsx');
    process.exit(1);
  }
} else {
  console.error('Error: Neither built nor source files found.');
  console.error('Please run "npm run build" to build the project.');
  process.exit(1);
}
