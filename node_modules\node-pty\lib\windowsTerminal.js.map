{"version": 3, "file": "windowsTerminal.js", "sourceRoot": "", "sources": ["../src/windowsTerminal.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;AAGH,uCAAkE;AAClE,qDAAoD;AAGpD,iCAAiC;AAEjC,IAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,IAAM,YAAY,GAAG,eAAe,CAAC;AAErC;IAAqC,mCAAQ;IAK3C,yBAAY,IAAa,EAAE,IAAwB,EAAE,GAA4B;QAAjF,YACE,kBAAM,GAAG,CAAC,SAqGX;QAnGC,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE9C,uBAAuB;QACvB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAClB,IAAI,GAAG,IAAI,IAAI,YAAY,CAAC;QAC5B,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;QAEjC,IAAI,GAAG,CAAC,QAAQ,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;SAC9D;QAED,IAAM,GAAG,GAAG,cAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChC,KAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,IAAI,uBAAY,CAAC;QACtC,KAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,IAAI,uBAAY,CAAC;QACtC,IAAM,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACrC,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY,CAAC;QAClD,IAAM,SAAS,GAAG,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAEtC,2BAA2B;QAC3B,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,6DAA6D;QAC7D,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,qBAAqB;QACrB,KAAI,CAAC,MAAM,GAAG,IAAI,iCAAe,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,KAAI,CAAC,KAAK,EAAE,KAAI,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACrI,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAErC,6CAA6C;QAC7C,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACjC,KAAI,CAAC,GAAG,GAAG,KAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAE5B,sEAAsE;QACtE,WAAW;QACX,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE;YAEhC,sCAAsC;YACtC,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC1D,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;oBAErB,sEAAsE;oBACtE,IAAI,CAAC,KAAI,CAAC,QAAQ,IAAI,KAAK,KAAK,MAAM,EAAE;wBAEtC,gEAAgE;wBAChE,SAAS;wBACT,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;wBAErB,+BAA+B;wBAC/B,KAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE;4BACxB,4DAA4D;4BAC5D,kEAAkE;4BAClE,4DAA4D;4BAC5D,aAAa;4BACb,EAAE,CAAC,GAAG,EAAE,CAAC;wBACX,CAAC,CAAC,CAAC;wBAEH,QAAQ;wBACR,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;qBAEtB;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,wCAAwC;YACxC,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,GAAG;gBAC1B,0BAA0B;gBAC1B,KAAI,CAAC,MAAM,EAAE,CAAC;gBAEd,uEAAuE;gBACvE,mBAAmB;gBACnB,yBAAyB;gBACzB,2BAA2B;gBAC3B,IAAU,GAAI,CAAC,IAAI,EAAE;oBACnB,IAAI,CAAO,GAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAO,GAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;wBAAE,OAAO;iBACpF;gBAED,uBAAuB;gBACvB,IAAI,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBACtC,MAAM,GAAG,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;gBACvB,KAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACxC,KAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QAEL,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,KAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,KAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,KAAI,CAAC,cAAc,EAAE,CAAC;;IACxB,CAAC;IAES,gCAAM,GAAhB,UAAiB,IAAY;QAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAEO,kCAAQ,GAAhB,UAAiB,IAAY;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IAEW,oBAAI,GAAlB,UAAmB,OAAyB;QAC1C,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IAEI,gCAAM,GAAb,UAAc,IAAY,EAAE,IAAY;QAAxC,iBASC;QARC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,EAAE;YAClG,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;QACD,IAAI,CAAC,YAAY,CAAC;YAChB,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,+BAAK,GAAZ;QAAA,iBAIC;QAHC,IAAI,CAAC,YAAY,CAAC;YAChB,KAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,iCAAO,GAAd;QAAA,iBAIC;QAHC,IAAI,CAAC,YAAY,CAAC;YAChB,KAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,8BAAI,GAAX,UAAY,MAAe;QAA3B,iBAQC;QAPC,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;aACtD;YACD,KAAI,CAAC,MAAM,EAAE,CAAC;YACd,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sCAAY,GAApB,UAAwB,UAAsB;QAA9C,iBAWC;QAVC,qCAAqC;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,OAAO;SACR;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,GAAG,EAAE,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,EAArB,CAAqB;SACjC,CAAC,CAAC;IACL,CAAC;IAEO,gCAAM,GAAd,UAAkB,UAA4B,EAAE,GAAM;QAAtD,iBAWC;QAVC,qCAAqC;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO;SACR;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,GAAG,EAAE,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,KAAI,EAAE,GAAG,CAAC,EAA1B,CAA0B;SACtC,CAAC,CAAC;IACL,CAAC;IAED,sBAAW,oCAAO;aAAlB,cAA+B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACnD,sBAAW,mCAAM;aAAjB,cAA8B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC,CAAC;;;OAAA;IACtF,sBAAW,kCAAK;aAAhB,cAA6B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC,CAAC;;;OAAA;IACtF,sBAAC;AAAD,CAAC,AA/LD,CAAqC,mBAAQ,GA+L5C;AA/LY,0CAAe"}