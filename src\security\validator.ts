import path from 'path';
import { logger } from '@/utils/logger';
import type { <PERSON>l<PERSON>all, RiskAssessment, RiskLevel } from '@/types';

export class RiskValidator {
  private criticalPatterns: RegExp[] = [];
  private highRiskPatterns: RegExp[] = [];
  private mediumRiskPatterns: RegExp[] = [];
  private safeCommands: Set<string> = new Set();

  constructor() {
    this.initializePatterns();
  }

  private initializePatterns(): void {
    // Critical risk patterns
    this.criticalPatterns = [
      /rm\s+-rf\s+\/$/,                    // rm -rf /
      /rm\s+-rf\s+\*$/,                    // rm -rf *
      /dd\s+if=/,                          // dd commands
      /mkfs/,                              // filesystem creation
      /fdisk/,                             // disk partitioning
      /format\s+[a-z]:/i,                  // Windows format
      /del\s+\/s/i,                        // Windows recursive delete
      /rmdir\s+\/s/i,                      // Windows recursive rmdir
      /shutdown|reboot|halt|poweroff/,     // System shutdown
      /:\(\)\{\s*:\|\:&\s*\}\s*;:/,       // Fork bomb
    ];

    // High risk patterns
    this.highRiskPatterns = [
      /sudo\s+/,                           // Sudo commands
      /su\s+/,                             // Switch user
      /chmod\s+777/,                       // Dangerous permissions
      /chown\s+/,                          // Change ownership
      /rm\s+/,                             // File deletion
      /del\s+/i,                           // Windows delete
      /move\s+/i,                          // Windows move
      /copy\s+.*\s+\/y/i,                  // Windows forced copy
      />\s*\/dev\/(null|zero|random)/,     // Device file operations
      /curl.*\|\s*sh/,                     // Pipe to shell
      /wget.*\|\s*sh/,                     // Pipe to shell
      /eval\s+/,                           // Code evaluation
      /exec\s+/,                           // Code execution
    ];

    // Medium risk patterns
    this.mediumRiskPatterns = [
      /npm\s+install\s+-g/,                // Global npm install
      /pip\s+install/,                     // Python package install
      /apt\s+install/,                     // Package installation
      /yum\s+install/,                     // Package installation
      /brew\s+install/,                    // Package installation
      /git\s+push/,                        // Git push
      /npm\s+publish/,                     // NPM publish
      /docker\s+run/,                      // Docker run
      /systemctl\s+(start|stop|restart)/,  // Service control
      /service\s+(start|stop|restart)/,    // Service control
      /crontab\s+-e/,                      // Cron editing
      />\s*\/etc\//,                       // Writing to system config
    ];

    // Safe commands (read-only operations)
    const safeCommandList = [
      'ls', 'dir', 'pwd', 'whoami', 'date', 'echo', 'cat', 'head', 'tail',
      'grep', 'find', 'locate', 'ps', 'top', 'htop', 'df', 'du', 'free',
      'uptime', 'uname', 'which', 'whereis', 'type', 'history', 'env',
      'git status', 'git log', 'git diff', 'git show', 'git branch',
      'npm list', 'npm outdated', 'npm audit', 'npm view',
      'node --version', 'npm --version', 'git --version',
      'python --version', 'pip --version', 'java -version',
    ];

    safeCommandList.forEach(cmd => this.safeCommands.add(cmd));
  }

  async assessRisk(toolCall: ToolCall): Promise<RiskAssessment> {
    const assessment: RiskAssessment = {
      level: 'safe',
      reasons: [],
      suggestions: [],
      autoApprove: false,
    };

    try {
      switch (toolCall.name) {
        case 'shell':
          this.assessShellCommand(toolCall, assessment);
          break;
        case 'file_delete':
          this.assessFileDelete(toolCall, assessment);
          break;
        case 'file_write':
        case 'file_create':
          this.assessFileWrite(toolCall, assessment);
          break;
        case 'file_move':
          this.assessFileMove(toolCall, assessment);
          break;
        default:
          this.assessGenericTool(toolCall, assessment);
      }

      // Set auto-approve flag based on risk level
      assessment.autoApprove = assessment.level === 'safe' || assessment.level === 'low';

      logger.debug('Risk assessment completed', {
        tool: toolCall.name,
        riskLevel: assessment.level,
        reasonCount: assessment.reasons.length,
      });

    } catch (error) {
      logger.error('Error during risk assessment', error);
      assessment.level = 'high';
      assessment.reasons.push('Error occurred during risk assessment');
      assessment.autoApprove = false;
    }

    return assessment;
  }

  private assessShellCommand(toolCall: ToolCall, assessment: RiskAssessment): void {
    const command = toolCall.arguments.command as string;
    const args = (toolCall.arguments.args as string[]) || [];
    const fullCommand = `${command} ${args.join(' ')}`.trim();

    // Check against critical patterns
    for (const pattern of this.criticalPatterns) {
      if (pattern.test(fullCommand)) {
        assessment.level = 'critical';
        assessment.reasons.push(`Command matches critical risk pattern: ${pattern.source}`);
        assessment.suggestions.push('Consider using safer alternatives or manual verification');
        return;
      }
    }

    // Check against high risk patterns
    for (const pattern of this.highRiskPatterns) {
      if (pattern.test(fullCommand)) {
        assessment.level = 'high';
        assessment.reasons.push(`Command matches high risk pattern: ${pattern.source}`);
        assessment.suggestions.push('Verify command necessity and consider safer alternatives');
      }
    }

    // Check against medium risk patterns
    for (const pattern of this.mediumRiskPatterns) {
      if (pattern.test(fullCommand)) {
        if (assessment.level === 'safe') {
          assessment.level = 'medium';
        }
        assessment.reasons.push(`Command matches medium risk pattern: ${pattern.source}`);
        assessment.suggestions.push('Review command parameters and ensure they are correct');
      }
    }

    // Check if it's a known safe command
    if (this.safeCommands.has(fullCommand) || this.safeCommands.has(command)) {
      if (assessment.level === 'safe') {
        assessment.suggestions.push('This is a read-only operation and should be safe');
      }
      return;
    }

    // Additional specific checks
    this.checkCommandSpecifics(fullCommand, assessment);

    // If no specific risk found but not in safe list, mark as low risk
    if (assessment.level === 'safe' && !this.safeCommands.has(command)) {
      assessment.level = 'low';
      assessment.reasons.push('Command not in known safe list');
    }
  }

  private checkCommandSpecifics(command: string, assessment: RiskAssessment): void {
    const lowerCommand = command.toLowerCase();

    // Check for file operations on system directories
    const systemPaths = ['/etc', '/usr', '/var', '/sys', '/proc', 'c:\\windows', 'c:\\program files'];
    for (const sysPath of systemPaths) {
      if (lowerCommand.includes(sysPath.toLowerCase())) {
        if (assessment.level === 'safe' || assessment.level === 'low') {
          assessment.level = 'medium';
        }
        assessment.reasons.push(`Operation on system directory: ${sysPath}`);
        assessment.suggestions.push('Be careful when modifying system directories');
      }
    }

    // Check for network operations
    if (lowerCommand.includes('curl') || lowerCommand.includes('wget') || lowerCommand.includes('fetch')) {
      if (assessment.level === 'safe') {
        assessment.level = 'low';
      }
      assessment.reasons.push('Network operation detected');
      assessment.suggestions.push('Verify the URL and ensure it\'s from a trusted source');
    }

    // Check for package manager operations
    if (lowerCommand.includes('install') || lowerCommand.includes('update') || lowerCommand.includes('upgrade')) {
      if (assessment.level === 'safe') {
        assessment.level = 'medium';
      }
      assessment.reasons.push('Package management operation');
      assessment.suggestions.push('Review packages being installed/updated');
    }
  }

  private assessFileDelete(toolCall: ToolCall, assessment: RiskAssessment): void {
    const filePath = toolCall.arguments.path as string;
    const recursive = toolCall.arguments.recursive as boolean;

    if (recursive) {
      assessment.level = 'high';
      assessment.reasons.push('Recursive deletion requested');
      assessment.suggestions.push('Ensure you want to delete the entire directory tree');
    } else {
      assessment.level = 'medium';
      assessment.reasons.push('File deletion operation');
    }

    // Check if deleting system files
    if (this.isSystemPath(filePath)) {
      assessment.level = 'critical';
      assessment.reasons.push('Attempting to delete system files');
      assessment.suggestions.push('Do not delete system files unless absolutely necessary');
    }

    // Check for dangerous patterns
    if (filePath.includes('*') || filePath.includes('..')) {
      assessment.level = 'high';
      assessment.reasons.push('Wildcard or parent directory reference in path');
      assessment.suggestions.push('Be specific about which files to delete');
    }
  }

  private assessFileWrite(toolCall: ToolCall, assessment: RiskAssessment): void {
    const filePath = toolCall.arguments.path as string;
    const content = toolCall.arguments.content as string;

    // Check if writing to system locations
    if (this.isSystemPath(filePath)) {
      assessment.level = 'high';
      assessment.reasons.push('Writing to system location');
      assessment.suggestions.push('Ensure you have proper permissions and need to modify system files');
    }

    // Check for executable files
    if (this.isExecutableFile(filePath)) {
      assessment.level = 'medium';
      assessment.reasons.push('Creating/modifying executable file');
      assessment.suggestions.push('Review the content of executable files carefully');
    }

    // Check content for suspicious patterns
    if (content && this.containsSuspiciousContent(content)) {
      assessment.level = 'high';
      assessment.reasons.push('Content contains potentially dangerous code');
      assessment.suggestions.push('Review the file content for security issues');
    }

    if (assessment.level === 'safe') {
      assessment.level = 'low';
      assessment.reasons.push('File write operation');
    }
  }

  private assessFileMove(toolCall: ToolCall, assessment: RiskAssessment): void {
    const source = toolCall.arguments.source as string;
    const destination = toolCall.arguments.destination as string;

    if (this.isSystemPath(source) || this.isSystemPath(destination)) {
      assessment.level = 'high';
      assessment.reasons.push('Moving files to/from system locations');
      assessment.suggestions.push('Ensure you have proper permissions for system file operations');
    } else {
      assessment.level = 'low';
      assessment.reasons.push('File move operation');
    }
  }

  private assessGenericTool(toolCall: ToolCall, assessment: RiskAssessment): void {
    // Most other tools are considered safe
    assessment.level = 'safe';
    assessment.suggestions.push(`${toolCall.name} operation should be safe`);
  }

  private isSystemPath(filePath: string): boolean {
    const systemPaths = [
      '/etc', '/usr', '/var', '/sys', '/proc', '/boot', '/root',
      'c:\\windows', 'c:\\program files', 'c:\\program files (x86)',
      'c:\\system32', 'c:\\syswow64',
    ];

    const normalizedPath = path.normalize(filePath).toLowerCase();
    return systemPaths.some(sysPath => 
      normalizedPath.startsWith(sysPath.toLowerCase())
    );
  }

  private isExecutableFile(filePath: string): boolean {
    const executableExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.scr', '.msi',
      '.sh', '.bash', '.zsh', '.fish', '.py', '.pl', '.rb',
      '.js', '.ts', '.jar', '.app', '.deb', '.rpm',
    ];

    const ext = path.extname(filePath).toLowerCase();
    return executableExtensions.includes(ext);
  }

  private containsSuspiciousContent(content: string): boolean {
    const suspiciousPatterns = [
      /eval\s*\(/,
      /exec\s*\(/,
      /system\s*\(/,
      /shell_exec\s*\(/,
      /passthru\s*\(/,
      /proc_open\s*\(/,
      /popen\s*\(/,
      /curl.*\|\s*sh/,
      /wget.*\|\s*sh/,
      /rm\s+-rf/,
      /dd\s+if=/,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(content));
  }

  // Configuration methods
  addCriticalPattern(pattern: RegExp): void {
    this.criticalPatterns.push(pattern);
    logger.debug('Critical pattern added', { pattern: pattern.source });
  }

  addHighRiskPattern(pattern: RegExp): void {
    this.highRiskPatterns.push(pattern);
    logger.debug('High risk pattern added', { pattern: pattern.source });
  }

  addMediumRiskPattern(pattern: RegExp): void {
    this.mediumRiskPatterns.push(pattern);
    logger.debug('Medium risk pattern added', { pattern: pattern.source });
  }

  addSafeCommand(command: string): void {
    this.safeCommands.add(command);
    logger.debug('Safe command added', { command });
  }

  removeSafeCommand(command: string): void {
    this.safeCommands.delete(command);
    logger.debug('Safe command removed', { command });
  }
}
