{"name": "kritrima-ai-cli", "version": "1.0.0", "description": "Production-ready AI-powered CLI tool system with agentic capabilities", "main": "dist/index.js", "bin": {"kritrima": "./bin/kritrima"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepare": "npm run build"}, "keywords": ["ai", "cli", "agent", "automation", "typescript", "openai", "deepseek", "ollama"], "author": "Kritrima AI CLI", "license": "MIT", "dependencies": {"openai": "^4.95.1", "commander": "^12.0.0", "inquirer": "^9.2.15", "chalk": "^5.3.0", "ora": "^8.0.1", "boxen": "^7.1.1", "chokidar": "^3.6.0", "glob": "^10.3.10", "fast-glob": "^3.3.2", "minimatch": "^9.0.3", "yaml": "^2.4.1", "dotenv": "^16.4.5", "winston": "^3.11.0", "zod": "^3.22.4", "nanoid": "^5.0.6", "semver": "^7.6.0", "cross-spawn": "^7.0.3", "strip-ansi": "^7.1.0", "ansi-escapes": "^6.2.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/inquirer": "^9.0.7", "@types/cross-spawn": "^6.0.6", "@types/semver": "^7.5.8", "@types/better-sqlite3": "^7.6.9", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "prettier": "^3.2.5", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/kritrima/ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima/ai-cli/issues"}, "homepage": "https://github.com/kritrima/ai-cli#readme"}