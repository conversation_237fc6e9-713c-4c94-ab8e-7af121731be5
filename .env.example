# Kritrima AI CLI Configuration
# Copy this file to .env and fill in your values

# AI Provider Configuration
KRITRIMA_PROVIDER=openai
KRITRIMA_MODEL=gpt-4
KRITRIMA_API_KEY=your_api_key_here
KRITRIMA_BASE_URL=
KRITRIMA_MAX_TOKENS=4096
KRITRIMA_TEMPERATURE=0.7

# Security Configuration
KRITRIMA_APPROVAL_MODE=suggest
KRITRIMA_WORKING_DIR=

# Provider-specific API Keys
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
AZURE_OPENAI_API_KEY=your_azure_api_key

# Logging Configuration
LOG_LEVEL=info
LOG_CONSOLE=false

# Development
NODE_ENV=production
