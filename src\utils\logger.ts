import winston from 'winston';
import path from 'path';
import os from 'os';
import fs from 'fs';

// Ensure log directory exists
const logDir = path.join(os.homedir(), '.kritrima', 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let output = `${timestamp} [${level}] ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      output += ` ${JSON.stringify(meta)}`;
    }
    
    return output;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { service: 'kritrima-cli' },
  transports: [
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // File transport for combined logs
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// Add console transport in development or when explicitly requested
if (process.env.NODE_ENV !== 'production' || process.env.LOG_CONSOLE === 'true') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: process.env.LOG_LEVEL || 'info',
  }));
}

// Create child loggers for different components
export const createLogger = (component: string) => {
  return logger.child({ component });
};

// Utility functions for structured logging
export const loggers = {
  agent: createLogger('agent'),
  session: createLogger('session'),
  context: createLogger('context'),
  ai: createLogger('ai'),
  tools: createLogger('tools'),
  security: createLogger('security'),
  cli: createLogger('cli'),
  storage: createLogger('storage'),
};

// Performance logging utility
export class PerformanceLogger {
  private startTime: number;
  private operation: string;
  private logger: winston.Logger;

  constructor(operation: string, logger: winston.Logger = logger) {
    this.operation = operation;
    this.logger = logger;
    this.startTime = Date.now();
    
    this.logger.debug(`Starting operation: ${operation}`);
  }

  end(metadata?: Record<string, unknown>): void {
    const duration = Date.now() - this.startTime;
    
    this.logger.info(`Operation completed: ${this.operation}`, {
      duration,
      ...metadata,
    });
  }

  error(error: Error, metadata?: Record<string, unknown>): void {
    const duration = Date.now() - this.startTime;
    
    this.logger.error(`Operation failed: ${this.operation}`, {
      duration,
      error: error.message,
      stack: error.stack,
      ...metadata,
    });
  }
}

// Error logging utility
export const logError = (
  message: string, 
  error: unknown, 
  metadata?: Record<string, unknown>
): void => {
  if (error instanceof Error) {
    logger.error(message, {
      error: error.message,
      stack: error.stack,
      ...metadata,
    });
  } else {
    logger.error(message, {
      error: String(error),
      ...metadata,
    });
  }
};

// Debug logging utility with conditional execution
export const logDebug = (
  message: string, 
  dataFn: () => Record<string, unknown>
): void => {
  if (logger.isDebugEnabled()) {
    logger.debug(message, dataFn());
  }
};

// Audit logging for security-sensitive operations
export const auditLog = (
  action: string,
  user: string,
  resource: string,
  result: 'success' | 'failure',
  metadata?: Record<string, unknown>
): void => {
  logger.info('AUDIT', {
    action,
    user,
    resource,
    result,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Request/Response logging for AI interactions
export const logAIInteraction = (
  provider: string,
  model: string,
  tokens: number,
  duration: number,
  success: boolean,
  metadata?: Record<string, unknown>
): void => {
  logger.info('AI_INTERACTION', {
    provider,
    model,
    tokens,
    duration,
    success,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Tool execution logging
export const logToolExecution = (
  toolName: string,
  args: Record<string, unknown>,
  result: {
    success: boolean;
    duration: number;
    output?: string;
    error?: string;
  },
  metadata?: Record<string, unknown>
): void => {
  logger.info('TOOL_EXECUTION', {
    tool: toolName,
    args,
    success: result.success,
    duration: result.duration,
    hasOutput: !!result.output,
    hasError: !!result.error,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Session activity logging
export const logSessionActivity = (
  sessionId: string,
  activity: string,
  metadata?: Record<string, unknown>
): void => {
  logger.info('SESSION_ACTIVITY', {
    sessionId,
    activity,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Configuration change logging
export const logConfigChange = (
  component: string,
  changes: Record<string, { from: unknown; to: unknown }>,
  metadata?: Record<string, unknown>
): void => {
  logger.info('CONFIG_CHANGE', {
    component,
    changes,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Log cleanup utility
export const cleanupLogs = async (olderThanDays: number = 30): Promise<void> => {
  try {
    const files = fs.readdirSync(logDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    for (const file of files) {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < cutoffDate) {
        fs.unlinkSync(filePath);
        logger.info('Log file cleaned up', { file, age: olderThanDays });
      }
    }
  } catch (error) {
    logger.error('Failed to cleanup logs', { error });
  }
};

// Export default logger
export default logger;
