import { z } from 'zod';

// Core Types
export interface AgentConfig {
  provider: AIProvider;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
  approvalMode: ApprovalMode;
  sessionId?: string;
  workingDirectory: string;
}

export type AIProvider = 'openai' | 'deepseek' | 'ollama' | 'azure';
export type ApprovalMode = 'suggest' | 'auto-edit' | 'full-auto';

// Session Management
export interface Session {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  workingDirectory: string;
  context: SessionContext;
  messages: ConversationMessage[];
  metadata: SessionMetadata;
}

export interface SessionContext {
  projectStructure: ProjectStructure;
  environmentInfo: EnvironmentInfo;
  recentFiles: string[];
  activeProcesses: ProcessInfo[];
  variables: Record<string, unknown>;
}

export interface SessionMetadata {
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  lastActivity: Date;
  tags: string[];
}

// Conversation
export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  tokens?: number;
  model?: string;
  provider?: AIProvider;
  executionTime?: number;
  approved?: boolean;
}

// Tool System
export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, unknown>;
  approved?: boolean;
  riskLevel: RiskLevel;
}

export interface ToolResult {
  id: string;
  toolCallId: string;
  success: boolean;
  output: string;
  error?: string;
  executionTime: number;
  metadata?: Record<string, unknown>;
}

export type RiskLevel = 'safe' | 'low' | 'medium' | 'high' | 'critical';

// File Operations
export interface FileOperation {
  type: 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'search' | 'grep';
  path: string;
  content?: string;
  destination?: string;
  pattern?: string;
  options?: FileOperationOptions;
}

export interface FileOperationOptions {
  recursive?: boolean;
  force?: boolean;
  backup?: boolean;
  encoding?: string;
  permissions?: string;
}

// Shell Commands
export interface ShellCommand {
  command: string;
  args: string[];
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  shell?: boolean;
}

export interface CommandResult {
  success: boolean;
  exitCode: number;
  stdout: string;
  stderr: string;
  executionTime: number;
  command: string;
}

// Project Structure
export interface ProjectStructure {
  root: string;
  type: ProjectType;
  packageManager?: PackageManager;
  framework?: string;
  language?: string;
  files: FileNode[];
  dependencies: Dependency[];
  scripts: Record<string, string>;
}

export type ProjectType = 'node' | 'python' | 'rust' | 'go' | 'java' | 'unknown';
export type PackageManager = 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo' | 'go' | 'maven' | 'gradle';

export interface FileNode {
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: Date;
  children?: FileNode[];
}

export interface Dependency {
  name: string;
  version: string;
  type: 'production' | 'development' | 'peer' | 'optional';
}

// Environment Info
export interface EnvironmentInfo {
  platform: NodeJS.Platform;
  arch: string;
  nodeVersion: string;
  npmVersion?: string;
  gitVersion?: string;
  shell: string;
  terminal: string;
  workingDirectory: string;
  homeDirectory: string;
  pathVariable: string[];
}

export interface ProcessInfo {
  pid: number;
  name: string;
  command: string;
  status: 'running' | 'stopped' | 'zombie';
  startTime: Date;
  cpuUsage?: number;
  memoryUsage?: number;
}

// Security & Validation
export interface SecurityPolicy {
  allowedCommands: string[];
  blockedCommands: string[];
  allowedPaths: string[];
  blockedPaths: string[];
  requireApproval: string[];
  maxExecutionTime: number;
  sandboxMode: boolean;
}

export interface RiskAssessment {
  level: RiskLevel;
  reasons: string[];
  suggestions: string[];
  autoApprove: boolean;
}

// Configuration Schemas
export const ConfigSchema = z.object({
  provider: z.enum(['openai', 'deepseek', 'ollama', 'azure']),
  model: z.string(),
  apiKey: z.string().optional(),
  baseUrl: z.string().url().optional(),
  maxTokens: z.number().positive().optional(),
  temperature: z.number().min(0).max(2).optional(),
  approvalMode: z.enum(['suggest', 'auto-edit', 'full-auto']),
  workingDirectory: z.string(),
  security: z.object({
    allowedCommands: z.array(z.string()).optional(),
    blockedCommands: z.array(z.string()).optional(),
    maxExecutionTime: z.number().positive().optional(),
    sandboxMode: z.boolean().optional(),
  }).optional(),
});

export type Config = z.infer<typeof ConfigSchema>;

// Error Types
export class AgentError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

export class ToolError extends AgentError {
  constructor(
    message: string,
    public toolName: string,
    details?: Record<string, unknown>
  ) {
    super(message, 'TOOL_ERROR', { toolName, ...details });
    this.name = 'ToolError';
  }
}

export class SecurityError extends AgentError {
  constructor(
    message: string,
    public riskLevel: RiskLevel,
    details?: Record<string, unknown>
  ) {
    super(message, 'SECURITY_ERROR', { riskLevel, ...details });
    this.name = 'SecurityError';
  }
}

// Event System
export interface AgentEvent {
  type: string;
  timestamp: Date;
  data: Record<string, unknown>;
}

export type EventHandler<T = Record<string, unknown>> = (event: AgentEvent & { data: T }) => void | Promise<void>;

// Streaming
export interface StreamChunk {
  type: 'text' | 'tool_call' | 'tool_result' | 'error' | 'done';
  content: string;
  metadata?: Record<string, unknown>;
}

export interface StreamHandler {
  onChunk: (chunk: StreamChunk) => void;
  onError: (error: Error) => void;
  onComplete: () => void;
}
