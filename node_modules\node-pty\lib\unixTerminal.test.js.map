{"version": 3, "file": "unixTerminal.test.js", "sourceRoot": "", "sources": ["../src/unixTerminal.test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAA8C;AAC9C,+BAAiC;AACjC,kCAAoC;AACpC,2BAA6B;AAC7B,yBAA2B;AAC3B,uBAAyB;AACzB,yBAA+B;AAC/B,mDAA6C;AAE7C,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC,CAAC;AAEnG,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;IAChC,QAAQ,CAAC,cAAc,EAAE;QACvB,QAAQ,CAAC,aAAa,EAAE;YACtB,EAAE,CAAC,6BAA6B,EAAE;gBAChC,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACnD,IAAI,MAA0B,CAAC;gBAC/B,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;oBAChC,kCAAkC;oBAClC,MAAM,GAAG,mBAAmB,CAAC;iBAC9B;gBACD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBACjC,qGAAqG;oBACrG,MAAM,GAAG,+BAA+B,CAAC;iBAC1C;gBACD,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,iBAAiB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAClG;gBACD,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE;YAChC,EAAE,CAAC,wBAAwB,EAAE,UAAC,IAAI;gBAChC,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,WAAW,EAAE,CAAE,IAAI,EAAE,WAAQ,aAAa,OAAG,CAAE,CAAC,CAAC;gBAC/E,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC1C,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBACnC,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,8CAA8C,EAAE,UAAC,IAAI;gBACtD,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,WAAW,EAAE,CAAE,IAAI,EAAE,WAAQ,aAAa,OAAG,CAAE,EAAE;oBAC7E,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC1C,MAAM,CAAC,EAAE,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC;oBAClC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gCAAgC,EAAE,UAAC,IAAI;gBACxC,IAAM,IAAI,GAAG,SAAS,CAAC;gBACvB,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;oBACtE,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC;gBACH,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI;oBACf,MAAM,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC1C,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC;oBACV,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC3G,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,EAAE;YACf,IAAI,IAAkB,CAAC;YAEvB,SAAS,CAAC;gBACR,IAAI,IAAI,EAAE;oBACR,IAAI,CAAC,KAAM,CAAC,OAAO,EAAE,CAAC;oBACtB,IAAI,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;iBACxB;YACH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4DAA4D,EAAE,UAAC,IAAI;gBACpE,IAAI,GAAG,2BAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE7B,IAAI,QAAQ,GAAG,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBAC1B,QAAQ,IAAI,IAAI,CAAC;gBACnB,CAAC,CAAC,CAAC;gBAEH,IAAI,SAAS,GAAG,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAO,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBAC3B,SAAS,IAAI,IAAI,CAAC;gBACpB,CAAC,CAAC,CAAC;gBAEH,0BAAS,CAAC;oBACR,IAAI,SAAS,KAAK,qBAAqB,IAAI,QAAQ,KAAK,UAAU,EAAE;wBAClE,IAAI,EAAE,CAAC;wBACP,OAAO,IAAI,CAAC;qBACb;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBAEZ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAC7B,IAAI,CAAC,MAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,OAAO,EAAE;YAChB,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,MAAM,CAAC,CAAC;YACtC,EAAE,CAAC,yDAAyD,EAAE,UAAC,IAAI;gBACjE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,MAAM;oBAC3B,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,cAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACrD,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,6BAA6B,EAAE;YACtC,EAAE,CAAC,qCAAqC,EAAE,UAAA,IAAI;gBAC5C,kEAAkE;gBAClE,kCAAkC;gBAClC,IAAM,IAAI,GAAG,slBAeZ,CAAC;gBACF,IAAM,MAAM,GAAa,EAAE,CAAC;gBAC5B,IAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACzC,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBACrC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACjD,UAAU,CAAC;4BACT,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAE,kBAAkB;4BAC1D,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAuB,mBAAmB;wBAC7D,CAAC,EAAE,GAAG,CAAC,CAAC;qBACT;yBAAM;wBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;qBACxD;gBACH,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBACZ,0DAA0D;oBAC1D,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACnE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACpE,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,6CAA6C,EAAE,UAAA,IAAI;gBACpD,oEAAoE;gBACpE,qEAAqE;gBACrE,wDAAwD;gBACxD,IAAM,IAAI,GAAG,2jBAeZ,CAAC;gBACF,IAAM,MAAM,GAAa,EAAE,CAAC;gBAC5B,IAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACzC,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBACrC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACjD,UAAU,CAAC;4BACT,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAE,kBAAkB;4BAC1D,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAuB,mBAAmB;wBAC7D,CAAC,EAAE,GAAG,CAAC,CAAC;qBACT;yBAAM;wBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;qBACxD;gBACH,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBACZ,0DAA0D;oBAC1D,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC1E,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACpE,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,6BAA6B,EAAE,UAAA,IAAI;gBACpC,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,MAAM,EAAE,CAAE,IAAI,EAAE,uFAED;iBAC5C,CAAC,CAAC;gBACH,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,IAAI,IAAI,KAAK,WAAW,EAAE;wBACxB,IAAI,CAAC,IAAI,EAAE,CAAC;qBACb;yBAAM;wBACL,MAAM,IAAI,IAAI,CAAC;qBAChB;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBACd,uBAAuB;oBACvB,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAC/B,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,sCAAsC,EAAE,UAAA,IAAI;gBAC7C,IAAI,cAAc,GAAG,CAAC,CAAC;gBACvB,IAAM,YAAY,GAAG,UAAS,CAAM;oBAClC,OAAO;wBACL,cAAc,IAAI,CAAC,CAAC;wBACpB,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;oBACvC,CAAC,CAAC;gBACJ,CAAC,CAAC;gBACF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;gBAElD,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,MAAM,EAAE,CAAE,IAAI,EAAE,qKAKnB;iBAC1B,CAAC,CAAC;gBACH,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,IAAI,IAAI,KAAK,WAAW,EAAE;wBACxB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBACtB;yBAAM;wBACL,MAAM,IAAI,IAAI,CAAC;qBAChB;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBACd,iDAAiD;oBACjD,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;oBACnD,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,OAAO,EAAE;YAChB,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACjC,EAAE,CAAC,uCAAuC,EAAE,UAAC,IAAI;oBAC/C,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,WAAW,CAAC,CAAC;oBAC3C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC9C,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;oBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,sBAAsB,EAAE,UAAC,IAAI;oBAC9B,IAAM,IAAI,GAAG,6WAQZ,CAAC;oBACF,IAAM,MAAM,GAAa,EAAE,CAAC;oBAC5B,IAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;oBAC/C,IAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;wBACvC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;qBAC1C,CAAC,CAAC;oBACH,IAAI,GAAG,GAAG,EAAE,CAAC;oBACb,CAAC,CAAC,MAAO,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;wBACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;4BACrC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;4BACjD,IAAI;gCACF,EAAE,CAAC,QAAQ,CAAC,WAAS,GAAG,YAAO,MAAQ,CAAC,CAAC;gCACzC,IAAI,CAAC,eAAe,CAAC,CAAC;6BACvB;4BAAC,OAAO,KAAK,EAAE;gCACd,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;6BAC5D;4BACD,UAAU,CAAC;gCACT,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAE,kBAAkB;gCAC1D,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAuB,mBAAmB;4BAC7D,CAAC,EAAE,GAAG,CAAC,CAAC;yBACT;6BAAM;4BACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;yBACxD;oBACH,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;wBACZ,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YACD,EAAE,CAAC,6BAA6B,EAAE,UAAC,IAAI;gBACrC,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;gBACpD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,MAAM;oBAC3B,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAC5B,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,8BAA8B,EAAE,UAAC,IAAI;gBACtC,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC;gBACpE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,MAAM;oBAC3B,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAC5B,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,+BAA+B,EAAE,UAAC,IAAI;gBACvC,IAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;gBACxD,IAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,MAAM,EAAE,CAAE,IAAI,EAAE,2FAEC;iBAC9C,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAO,IAAI;;;;;qCACrB,CAAA,IAAI,KAAK,WAAW,CAAA,EAApB,wBAAoB;gCACtB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gCACjC,qBAAM,UAAU,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,EAAE,IAAI,CAAC,EAAA;;gCAAlC,SAAkC,CAAC;gCAC7B,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;gCAC3D,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;gCAC1D,IAAI,EAAE,CAAC;;;;;qBAEV,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}