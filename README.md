# 🤖 Kritrima AI CLI

A production-ready, AI-powered command-line interface with agentic capabilities that lives and breathes in your local environment. Built with TypeScript 5.8.3 and Node.js 20+, supporting Windows 11 WSL, macOS, and Linux.

## ✨ Features

### 🧠 AI-Powered Intelligence
- **Multi-Provider Support**: OpenAI, Deepseek, Ollama, Azure OpenAI
- **Universal SDK Strategy**: Single OpenAI SDK for maximum compatibility
- **Streaming Responses**: Real-time AI interaction with live feedback
- **Tool Calling**: Advanced function calling with parallel execution

### 🛠️ Comprehensive Tool System
- **Shell Commands**: Execute any terminal command safely
- **File Operations**: Read, write, create, delete, move, copy, search
- **System Information**: Hardware, OS, processes, network details
- **Project Analysis**: Automatic discovery and indexing
- **Package Management**: Smart dependency handling

### 🔒 Enterprise-Grade Security
- **Multi-Level Approval System**: Suggest, Auto-Edit, Full-Auto modes
- **Risk Assessment**: Intelligent command validation
- **Sandboxed Execution**: Safe command execution environment
- **Audit Logging**: Complete operation tracking

### 💾 Session & Context Management
- **Persistent Sessions**: Resume conversations across CLI sessions
- **Intelligent Context**: Automatic project structure discovery
- **Dynamic Updates**: Real-time environment change detection
- **Cross-Platform**: Works seamlessly on Windows, macOS, Linux

### 🎯 Production Ready
- **Modern Architecture**: TypeScript, async/await, event-driven
- **Robust Error Handling**: Graceful degradation and recovery
- **Comprehensive Logging**: Winston-based structured logging
- **Configuration Management**: YAML-based with environment overrides

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/kritrima/ai-cli.git
cd ai-cli

# Install dependencies
npm install

# Build the project
npm run build

# Make executable globally
npm link
```

### Configuration

1. **Copy environment template:**
```bash
cp .env.example .env
```

2. **Set your API key:**
```bash
# For OpenAI
export OPENAI_API_KEY="your_api_key_here"

# For Deepseek
export DEEPSEEK_API_KEY="your_api_key_here"

# Or configure via CLI
kritrima config set apiKey your_api_key_here
```

3. **Initialize configuration:**
```bash
# Initialize for OpenAI (default)
kritrima config init openai

# Or for other providers
kritrima config init deepseek
kritrima config init ollama
kritrima config init azure
```

### First Run

```bash
# Start interactive mode
kritrima

# Or execute a single command
kritrima exec "List all files in the current directory"

# Show help
kritrima --help
```

## 📖 Usage

### Interactive Mode

```bash
kritrima
```

Start a conversation with the AI assistant:

```
🤖 Kritrima AI CLI
Your intelligent command-line assistant

[12345678] kritrima> Create a new React component called Button
🔧 Creating React component...
✅ Component created successfully at ./src/components/Button.tsx

[12345678] kritrima> Run the tests
🔧 Executing: npm test
✅ All tests passed (15 tests, 0 failures)
```

### One-Shot Commands

```bash
# Execute single commands
kritrima exec "Show system information"
kritrima exec "Install the latest dependencies"
kritrima exec "Find all TODO comments in the codebase"
```

### Configuration Management

```bash
# Show current configuration
kritrima config show

# Set configuration values
kritrima config set provider deepseek
kritrima config set model deepseek-chat
kritrima config set approvalMode auto-edit

# Validate configuration
kritrima config validate
```

### Session Management

```bash
# List all sessions
kritrima session list

# Delete old sessions
kritrima session delete <session-id>

# Clean up old sessions
kritrima session cleanup --days 30
```

## 🔧 Configuration

### Providers

#### OpenAI
```yaml
provider: openai
model: gpt-4
apiKey: your_openai_api_key
```

#### Deepseek
```yaml
provider: deepseek
model: deepseek-chat
apiKey: your_deepseek_api_key
baseUrl: https://api.deepseek.com/v1
```

#### Ollama (Local)
```yaml
provider: ollama
model: llama2
baseUrl: http://localhost:11434/v1
apiKey: ollama
```

#### Azure OpenAI
```yaml
provider: azure
model: gpt-4
apiKey: your_azure_api_key
baseUrl: https://your-resource.openai.azure.com/
```

### Security Settings

```yaml
security:
  allowedCommands:
    - ls
    - cat
    - grep
  blockedCommands:
    - rm -rf /
    - format
  maxExecutionTime: 30000
  sandboxMode: false
```

### Approval Modes

- **suggest**: Always ask for approval (safest)
- **auto-edit**: Auto-approve file operations, ask for commands
- **full-auto**: Auto-approve everything except critical operations

## 🛠️ Available Tools

### Shell Commands
```bash
kritrima exec "Run npm install"
kritrima exec "Show running processes"
kritrima exec "Check git status"
```

### File Operations
```bash
kritrima exec "Read the package.json file"
kritrima exec "Create a new file called config.ts"
kritrima exec "Find all .ts files in src directory"
kritrima exec "Search for 'TODO' in all files"
```

### System Information
```bash
kritrima exec "Show system information"
kritrima exec "Display memory usage"
kritrima exec "List network interfaces"
```

### Project Analysis
```bash
kritrima exec "Analyze the project structure"
kritrima exec "Show all dependencies"
kritrima exec "Find unused imports"
```

## 🔒 Security Features

### Risk Assessment
The system automatically assesses the risk level of each command:

- **Safe**: Read-only operations (ls, cat, grep)
- **Low**: Basic file operations (mkdir, touch)
- **Medium**: Package installations, git operations
- **High**: File deletions, permission changes
- **Critical**: System-level operations (rm -rf /, format)

### Approval System
Commands are automatically approved, blocked, or require user confirmation based on:

- Risk level assessment
- Command patterns and signatures
- User-configured approval mode
- Previous approval decisions

### Audit Logging
All operations are logged with:

- Command executed
- User approval status
- Execution results
- Timestamps and metadata
- Risk assessment details

## 🏗️ Architecture

### Core Components

```
src/
├── core/           # Agent orchestrator and session management
├── ai/             # AI provider integrations and streaming
├── tools/          # Tool system (shell, files, system)
├── security/       # Approval system and risk validation
├── cli/            # Terminal interface and commands
├── storage/        # Session persistence and context indexing
├── utils/          # Logging, configuration, utilities
└── types/          # TypeScript type definitions
```

### Event-Driven Architecture

The system uses an event-driven architecture for:

- Real-time UI updates
- Tool execution monitoring
- Context change detection
- Session state management
- Error handling and recovery

### Streaming & Real-Time

- **Streaming AI Responses**: See AI thinking in real-time
- **Live Tool Execution**: Watch commands execute with feedback
- **Context Updates**: Dynamic environment change detection
- **Session Persistence**: Resume conversations seamlessly

## 🧪 Development

### Setup Development Environment

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Building

```bash
# Build for production
npm run build

# Build and watch for changes
npm run build:watch
```

## 📊 Monitoring & Logging

### Log Levels
- **error**: Critical errors and failures
- **warn**: Warning conditions
- **info**: General information (default)
- **debug**: Detailed debugging information

### Log Locations
- **Console**: Real-time output (development)
- **Files**: `~/.kritrima/logs/`
  - `error.log`: Error-level logs only
  - `combined.log`: All log levels

### Metrics & Analytics
- Command execution statistics
- AI provider usage and performance
- Tool execution success rates
- Session activity patterns

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for the GPT models and API
- Deepseek for their competitive AI models
- Ollama for local AI model support
- The TypeScript and Node.js communities

## 📞 Support

- **Documentation**: [docs.kritrima.ai](https://docs.kritrima.ai)
- **Issues**: [GitHub Issues](https://github.com/kritrima/ai-cli/issues)
- **Discussions**: [GitHub Discussions](https://github.com/kritrima/ai-cli/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ by the Kritrima team**
