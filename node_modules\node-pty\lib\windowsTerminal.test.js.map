{"version": 3, "file": "windowsTerminal.test.js", "sourceRoot": "", "sources": ["../src/windowsTerminal.test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,uBAAyB;AACzB,+BAAiC;AACjC,qDAAoD;AACpD,2BAA6B;AAC7B,gCAAkC;AAYlC,SAAS,mBAAmB,CAAC,YAA2B,EAAE,UAAwB,EAAE,SAAwB;IAAlD,2BAAA,EAAA,gBAAwB;IAAE,0BAAA,EAAA,gBAAwB;IAC1G,OAAO,IAAI,OAAO,CAAO,UAAA,OAAO;QAC9B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAM,QAAQ,GAAG,WAAW,CAAC;YAC3B,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;gBAC3B,IAAI,OAAO,GAAG,IAAI,CAAC;gBACnB,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAf,CAAe,CAAC,CAAC;gBACjE,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;oBACd,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;wBACrB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE;4BAChC,OAAO,GAAG,KAAK,CAAC;yBACjB;qBACF;yBAAM;wBACL,IAAI,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE;4BAC/B,OAAO,GAAG,KAAK,CAAC;yBACjB;qBACF;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,OAAO,EAAE;oBACX,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,EAAE,CAAC;oBACV,OAAO;iBACR;gBACD,KAAK,EAAE,CAAC;gBACR,IAAI,KAAK,GAAG,UAAU,IAAI,SAAS,EAAE;oBACnC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAG,CAAC,UAAK,YAAY,CAAC,CAAC,CAAG,EAA1B,CAA0B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC5E,MAAM,CAAC,IAAI,CAAC,mCAAiC,cAAgB,CAAC,CAAC;oBAC/D,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW,EAAE,IAAY,EAAE,UAAwB,EAAE,SAAwB;IAAlD,2BAAA,EAAA,gBAAwB;IAAE,0BAAA,EAAA,gBAAwB;IAC3G,OAAO,IAAI,OAAO,CAA8B,UAAA,OAAO;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAM,QAAQ,GAAG,WAAW,CAAC;YAC3B,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;gBAC3B,IAAM,QAAQ,GAAgC,EAAE,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;oBAC/C,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,IAAM,IAAI,GAAgC,EAAE,CAAC;;oBAE3C,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;oBAClC,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,EAAtB,CAAsB,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;wBAC1C,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBACtC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;gBALrB,OAAO,QAAQ,CAAC,MAAM;;iBAMrB;gBACD,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC;gBACrC,IAAI,OAAO,EAAE;oBACX,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,CAAC;oBACd,OAAO;iBACR;gBACD,KAAK,EAAE,CAAC;gBACR,IAAI,KAAK,GAAG,UAAU,IAAI,SAAS,EAAE;oBACnC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,kCAAgC,IAAI,kBAAa,IAAI,CAAC,MAAQ,CAAC,CAAC;iBAC7E;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;IAChC,QAAQ,CAAC,iBAAiB,EAAE;QAC1B,QAAQ,CAAC,MAAM,EAAE;YACf,EAAE,CAAC,iCAAiC,EAAE,UAAC,IAAI;gBACzC,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,iFAAiF;gBAC3E,IAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAgB;gBAC3D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACpD,sBAAsB;gBACtB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACzB,sBAAsB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;oBACtD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,CAAC;oBAC1D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;oBACjE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,aAAa,CAAC,CAAC;oBAC9D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,UAAU,CAAC,CAAC;oBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,IAAM,YAAY,GAAkB,EAAE,CAAC;oBACvC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAClC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAClC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;oBACjC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAClC,mBAAmB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;wBACrC,2BAA2B;wBAC3B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC1B,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE;YACjB,EAAE,CAAC,oEAAoE,EAAE;gBACvE,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACpD,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAnB,CAAmB,CAAC,CAAC;gBACzC,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;gBACvC,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,sEAAsE,EAAE,UAAC,IAAI;gBAC9E,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9C,IAAK,CAAC,MAAM,CAAC;oBACjB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;wBACd,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;wBACvC,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;oBACH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE;YAC9B,EAAE,CAAC,+DAA+D,EAAE,UAAC,IAAI;gBACvE,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;gBAC9E,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;oBAC/B,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;iBAC3B;gBAED,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;gBAC3D,IAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAI,OAAO,CAAC,GAAG,CAAC,MAAM,wBAAqB,CAAC,CAAC;gBACzE,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;gBAEtC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;oBACjC,wCAAwC;oBACxC,OAAO;iBACR;gBACD,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,aAAa,EAAE,uBAAuB,EAAE,EAAE,CAAC,CAAC;gBAC7E,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBACd,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC9C,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,KAAK,EAAE;YACd,EAAE,CAAC,+CAA+C,EAAE,UAAC,IAAI;gBACvD,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAC,CAAC,CAAC;gBACrF,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBACd,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;oBACtC,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,UAAU,EAAE;YACnB,EAAE,CAAC,uCAAuC,EAAE,UAAC,IAAI;gBAC/C,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACvD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBACtB,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,UAAC,IAAI;gBACnD,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBACzD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;oBACnB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBACtB,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE;YACjB,EAAE,CAAC,qBAAqB,EAAE,UAAC,IAAI;gBAC7B,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;gBACtE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACrB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBACd,IAAI,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}